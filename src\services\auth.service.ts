import axios from 'axios';
import { authUtils } from '@/utils/auth.utils';
import { toast } from 'sonner';
import { API_BASE_URL } from '@/config';

const API_URL = `${API_BASE_URL}/api/users`;

// Create axios instance with interceptors
const apiClient = axios.create({
  baseURL: API_URL,
  withCredentials: true, // Important for cookies
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },
  // Increase timeout for debugging
  timeout: 10000
});

// Add request interceptor to include CSRF token
apiClient.interceptors.request.use(
  async config => {
    // Skip for GET requests
    if (config.method?.toLowerCase() === 'get') {
      return config;
    }

    // For non-GET requests, ensure we have a valid token
    // Skip for login and refresh-token endpoints
    if (!config.url?.includes('refresh-token') && !config.url?.includes('login')) {
      await authUtils.ensureValidToken();
    }

    // Add CSRF token to headers for non-GET requests
    const csrfToken = authUtils.getCsrfToken();
    if (csrfToken) {
      config.headers['X-CSRF-Token'] = csrfToken;
    }

    return config;
  },
  error => Promise.reject(error)
);

// Add response interceptor
apiClient.interceptors.response.use(
  response => response,
  async error => {
    // Skip 401 handling for login endpoint
    if (error.config?.url === '/login' && error.response?.status === 401) {
      return Promise.reject(error);
    }

    // Handle 401 errors (unauthorized)
    if (error.response?.status === 401) {
      // Try to refresh the token if not already trying to refresh
      if (!error.config?.url?.includes('refresh-token')) {
        try {
          await authService.refreshToken();

          // Retry the original request
          const newConfig = { ...error.config };

          // Update CSRF token for the retry
          if (newConfig.method?.toLowerCase() !== 'get') {
            const csrfToken = authUtils.getCsrfToken();
            if (csrfToken) {
              newConfig.headers['X-CSRF-Token'] = csrfToken;
            }
          }

          return axios(newConfig);
        } catch (refreshError) {
          // Clear auth data but don't redirect if we're already on the login page
          authUtils.clearAuthData();

          // Only redirect if we're not already on the login page
          const isLoginPage = window.location.pathname === '/' ||
                             window.location.pathname === '/en' ||
                             window.location.pathname === '/fr';

          if (!isLoginPage) {
            toast.error('Session expired. Please login again.');
            window.location.href = '/';
          }

          return Promise.reject(refreshError);
        }
      }

      // If we're already trying to refresh, just clear auth and redirect if needed
      authUtils.clearAuthData();

      // Only redirect if we're not already on the login page
      const isLoginPage = window.location.pathname === '/' ||
                         window.location.pathname === '/en' ||
                         window.location.pathname === '/fr';

      if (!isLoginPage) {
        toast.error('Session expired. Please login again.');
        window.location.href = '/';
      }
    }

    return Promise.reject(error);
  }
);

export interface LoginResponse {
  status: string;
  data: {
    user: {
      id: number;
      username: string;
      role: string;
      balance: number;
      currency: string;
    };
    csrfToken: string;
  };
}

export interface RefreshTokenResponse {
  status: string;
  data: {
    csrfToken: string;
  };
}

export interface UserDetailsResponse {
  status: string;
  data: {
    id: number;
    username: string;
    role: string;
    balance: string;
    currency: string;
    is_active: boolean;
    is_banned: boolean;
    last_login: string;
    parent_id: number | null;
    createdAt: string;
  };
}

export const authService = {
  async login(username: string, password: string): Promise<LoginResponse> {
    try {
      // Use axios directly for login to bypass the interceptors
      const response = await axios.post(`${API_URL}/login`, {
        username,
        password
      }, {
        withCredentials: true // Important for cookies
      });

      // Store user data
      if (response.data?.data?.user) {
        authUtils.setUserData(response.data.data.user);
      }

      // Check if we have a CSRF token in the response
      if (response.data?.data?.csrfToken) {
        // Store the token in cookie
        authUtils.storeCsrfToken(response.data.data.csrfToken);
      }

      return response.data;
    } catch (error) {
      throw error;
    }
  },

  async refreshToken(): Promise<RefreshTokenResponse> {
    try {
      const response = await axios.post(`${API_URL}/refresh-token`, {}, {
        withCredentials: true // Important for cookies
      });

      // Check if we have a CSRF token in the response
      if (response.data?.data?.csrfToken) {
        // Store the token in cookie
        authUtils.storeCsrfToken(response.data.data.csrfToken);
      }

      return response.data;
    } catch (error) {
      throw error;
    }
  },

  async getUserDetails(): Promise<UserDetailsResponse> {
    try {
      // Ensure we have a valid token before making the request
      await authUtils.ensureValidToken();

      const response = await apiClient.get('/details');
      return response.data;
    } catch (error: unknown) {
      if (axios.isAxiosError(error) && error.response?.status === 401) {
        authUtils.clearAuthData();
        window.location.href = '/';
      }
      throw error;
    }
  },

  async logout(): Promise<void> {
    try {
      // Ensure we have a valid token before making the request
      await authUtils.ensureValidToken();

      // Invalidate tokens on the server
      await apiClient.post('/logout');
    } catch (error) {
      console.log(error);
      // Silent fail - we're logging out anyway
    } finally {
      // Always clear local auth data
      authUtils.clearAuthData();
      // Short delay before redirect to allow toast to show
      setTimeout(() => {
        window.location.href = '/';
      }, 500);
    }
  }
};
