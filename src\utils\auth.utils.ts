import { AES, enc } from 'crypto-js';
import { User } from '@/services/user.service';

const ENCRYPTION_KEY = process.env.NEXT_PUBLIC_ENCRYPTION_KEY || 'your-fallback-key';

// Helper to check if we're on the client side
const isClient = typeof window !== 'undefined';

// Define the type for authService to avoid circular dependency
interface RefreshTokenResponse {
  status: string;
  data: {
    csrfToken: string;
  };
}

type AuthServiceType = {
  refreshToken: () => Promise<RefreshTokenResponse>;
};

// Will be initialized later to avoid circular dependency
let authService: AuthServiceType | null = null;

export const authUtils = {
  // Store user data only (no tokens)
  setUserData: (user: User) => {
    if (!isClient) return;

    try {
      // Encrypt and store user data
      const encryptedUserData = AES.encrypt(
        JSON.stringify(user),
        ENCRYPTION_KEY
      ).toString();

      // Store encrypted user data
      window.localStorage.setItem('userData', encryptedUserData);
    } catch (error) {
      console.error('Error storing user data:', error);
      throw new Error('Failed to store user data');
    }
  },

  // Get CSRF token from cookie
  getCsrfToken: (): string | null => {
    if (!isClient) return null;

    const cookies = document.cookie.split(';');
    const csrfCookie = cookies.find(c => c.trim().startsWith('csrf_token='));
    if (!csrfCookie) {
      return null;
    }

    return decodeURIComponent(csrfCookie.split('=')[1].trim());
  },

  // Function to ensure we have a valid token
  ensureValidToken: async (): Promise<boolean> => {
    try {
      // Initialize authService if needed
      await new Promise<void>((resolve) => {
        if (authService) {
          resolve();
          return;
        }

        // Use dynamic import
        import('@/services/auth.service').then(module => {
          authService = module.authService;
          resolve();
        }).catch(() => {
          resolve(); // Resolve anyway to continue execution
        });
      });

      // If authService is still null after initialization, throw an error
      if (!authService) {
        throw new Error('Failed to initialize authService');
      }

      // If we don't have a CSRF token, we need to refresh
      if (!authUtils.getCsrfToken()) {
        await authService.refreshToken();
        return true;
      }

      return true;
    } catch (error) {
      console.log(error);
      // Only clear auth data if we're not on the login page
      if (typeof window !== 'undefined' &&
          window.location.pathname !== '/' &&
          window.location.pathname !== '/en' &&
          window.location.pathname !== '/ar' &&
          window.location.pathname !== '/fr') {
        authUtils.clearAuthData();
      }
      return false;
    }
  },

  // Store CSRF token in cookie only
  storeCsrfToken: (token: string): void => {
    if (!isClient || !token) return;

    try {
      // Set as cookie only - never store in localStorage
      document.cookie = `csrf_token=${token}; path=/; max-age=86400; samesite=lax`;
    } catch (error) {
      // Silent fail
      console.log(error);
    }
  },

  getUserData: (): User | null => {
    if (!isClient) return null;

    try {
      const encryptedUserData = window.localStorage.getItem('userData');
      if (!encryptedUserData) return null;

      const decryptedBytes = AES.decrypt(encryptedUserData, ENCRYPTION_KEY);
      const decryptedData = decryptedBytes.toString(enc.Utf8);

      return decryptedData ? JSON.parse(decryptedData) : null;
    } catch (error) {
      console.error('Error retrieving user data:', error);
      return null;
    }
  },

  clearAuthData: () => {
    if (!isClient) return;

    try {
      // Clear user data from localStorage
      window.localStorage.removeItem('userData');

      // Clear csrf_token cookie - don't check localStorage for CSRF token
      document.cookie = 'csrf_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; samesite=lax';
    } catch (error) {
      console.log(error);
      // Silent fail
    }

    // Don't force reload - let the calling code handle navigation
    // This prevents unexpected redirects during login
  },

  isAuthenticated: async (): Promise<boolean> => {
    if (!isClient) return false;

    const userData = authUtils.getUserData();

    if (!userData) {
      return false;
    }

    // Ensure we have a valid token
    return await authUtils.ensureValidToken();
  },

  updateUserData: (updates: Partial<User>): void => {
    if (!isClient) return;

    const currentData = authUtils.getUserData();
    if (!currentData) return;

    const updatedUser = { ...currentData, ...updates };
    authUtils.setUserData(updatedUser);
  },

  // Added the missing handleBalanceRefresh function
  handleBalanceRefresh: async () => {
    try {
      // Ensure we have a valid token before making the request
      await authUtils.ensureValidToken();

      const response = await fetch('/api/users/details', {
        credentials: 'include' // Important for cookies
      });

      if (response.ok) {
        const data = await response.json();
        if (data.data) {
          authUtils.updateUserData({
            balance: data.data.balance,
            currency: data.data.currency
          });
          return data.data;
        }
      }
    } catch (error) {
      console.error('Failed to refresh balance:', error);
      throw error;
    }
  }
};
