'use client';

import { useEffect, useRef } from 'react';
import { authService } from '@/services/auth.service';
import { authUtils } from '@/utils/auth.utils';

// 14 minutes in milliseconds
const REFRESH_INTERVAL = 14 * 60 * 1000;

export function TokenRefreshManager() {
  const refreshTimerRef = useRef<NodeJS.Timeout | null>(null);

  const refreshToken = async () => {
    try {
      // Only refresh if user is logged in
      if (authUtils.getUserData()) {
        await authService.refreshToken();
        console.log('Token refreshed successfully');
      }
    } catch (error) {
      console.error('Failed to refresh token:', error);
    }
  };

  useEffect(() => {
    // Clear any existing timer
    if (refreshTimerRef.current) {
      clearInterval(refreshTimerRef.current);
    }

    // Set up token refresh interval
    refreshTimerRef.current = setInterval(refreshToken, REFRESH_INTERVAL);

    // Initial token refresh
    refreshToken();

    // Clean up on unmount
    return () => {
      if (refreshTimerRef.current) {
        clearInterval(refreshTimerRef.current);
      }
    };
  }, []);

  // This component doesn't render anything
  return null;
}
