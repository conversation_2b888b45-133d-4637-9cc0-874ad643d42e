"use client";

import { useTranslations } from 'next-intl';
import { useState, useEffect } from 'react';

export default function Footer() {
  const casinoName = process.env.NEXT_PUBLIC_CASINO_NAME || 'Casino Admin';
  const [currentYear, setCurrentYear] = useState<number | null>(null);
  const t = useTranslations('footerComponent');

  useEffect(() => {
    setCurrentYear(new Date().getFullYear());
  }, []);

  return (
    <footer className="bg-[var(--color-dark)] border-t border-[var(--color-mid)]/50 py-4">
      <div className="container mx-auto text-center text-sm text-[var(--color-light)]">
        {t('allRightsReserved')} {currentYear || new Date().getFullYear()}  {casinoName}.
      </div>
    </footer>
  );
}
