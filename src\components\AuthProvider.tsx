'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { authUtils } from '@/utils/auth.utils';

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    const checkAuth = async () => {
      try {
        // Check if we're on the login page
        const isLoginPage = window.location.pathname === '/' ||
                           window.location.pathname === '/en' ||
                           window.location.pathname === '/fr';

        if (isLoginPage) {
          setIsLoading(false);
          return;
        }

        // Try to get user data from localStorage
        const userData = authUtils.getUserData();

        if (!userData) {
          // No user data, redirect to login
          router.push('/');
          return;
        }

        // Ensure we have a valid token
        try {
          const isValid = await authUtils.ensureValidToken();

          if (!isValid) {
            router.push('/');
            return;
          }

          setIsLoading(false);
        } catch (error) {
          // Check if we're in development mode
          if (process.env.NODE_ENV === 'development') {
            console.log(error);
            // In development, we'll continue anyway to help with debugging
            setIsLoading(false);
          } else {
            // In production, we'll clear auth data and redirect to login
            authUtils.clearAuthData();
            router.push('/');
            return;
          }
        }
      } catch (error) {
        console.log(error);
        router.push('/');
      }
    };

    checkAuth();
  }, [router]);

  // Add a styled loading indicator
  if (isLoading) {
    return (
      <div className="fixed inset-0 flex items-center justify-center bg-gray-800 bg-opacity-75 z-50">
        <div className="bg-white p-5 rounded-lg shadow-lg flex flex-col items-center">
          <div className="w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mb-4"></div>
          <p className="text-gray-700">Loading...</p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}
